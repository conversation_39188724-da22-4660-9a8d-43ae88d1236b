use anyhow::{Result, anyhow};
use std::net::{IpAddr, Ipv4Addr};
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use log::{info, warn, error, debug};
use tun_easytier::{Configuration, create_as_async, AsyncDevice};

use crate::traits::{DnsManager, TrafficRouter, BackendProxy, ProxyMode};
use crate::packet::{PacketProcessor, TcpState};
use crate::stats::{record_traffic, record_connection_start, record_connection_end};

/// TUN/TAP interface manager
pub struct TunManager {
    device: Option<Arc<AsyncDevice>>,
    packet_processor: Arc<PacketProcessor>,
    dns_manager: Arc<dyn DnsManager>,
    traffic_router: Arc<dyn TrafficRouter>,
    backend_proxy: Arc<dyn BackendProxy>,
    is_running: Arc<RwLock<bool>>,
    tun_ip: Ipv4Addr,
    tun_netmask: Ipv4Addr,
    interface_name: String,
}

impl TunManager {
    pub fn new(
        dns_manager: Arc<dyn DnsManager>,
        traffic_router: Arc<dyn TrafficRouter>,
        backend_proxy: Arc<dyn BackendProxy>,
    ) -> Self {
        Self {
            device: None,
            packet_processor: Arc::new(PacketProcessor::new()),
            dns_manager,
            traffic_router,
            backend_proxy,
            is_running: Arc::new(RwLock::new(false)),
            tun_ip: Ipv4Addr::new(10, 0, 0, 1),
            tun_netmask: Ipv4Addr::new(255, 255, 255, 0),
            interface_name: "tun-proxy".to_string(),
        }
    }

    /// Start the TUN interface
    pub async fn start(&mut self) -> Result<()> {
        if *self.is_running.read().await {
            return Ok(());
        }

        info!("Starting TUN interface...");

        // Create TUN device configuration
        let mut config = Configuration::default();
        config.tun_name(&self.interface_name)
              .address(self.tun_ip)
              .netmask(self.tun_netmask)
              .mtu(1500)
              .up();

        // Create TUN device
        match create_as_async(&config) {
            Ok(device) => {
                info!("TUN device '{}' created successfully", self.interface_name);
                self.device = Some(Arc::new(device));
                *self.is_running.write().await = true;

                // Start packet processing loop
                self.start_packet_loop().await?;

                info!("TUN interface started successfully");
                Ok(())
            }
            Err(e) => {
                error!("Failed to create TUN device: {}", e);
                Err(anyhow!("Failed to create TUN device: {}", e))
            }
        }
    }

    /// Stop the TUN interface
    pub async fn stop(&mut self) -> Result<()> {
        if !*self.is_running.read().await {
            return Ok(());
        }

        info!("Stopping TUN interface...");
        *self.is_running.write().await = false;

        // Close TUN device
        if let Some(device) = self.device.take() {
            drop(device);
            info!("TUN device closed");
        }

        info!("TUN interface stopped");
        Ok(())
    }

    /// Start the packet processing loop
    async fn start_packet_loop(&mut self) -> Result<()> {
        let device = self.device.as_ref()
            .ok_or_else(|| anyhow!("TUN device not initialized"))?;

        let (tx, mut rx) = mpsc::channel::<Vec<u8>>(1000);
        let is_running = Arc::clone(&self.is_running);
        let dns_manager = Arc::clone(&self.dns_manager);
        let traffic_router = Arc::clone(&self.traffic_router);
        let backend_proxy = Arc::clone(&self.backend_proxy);
        let packet_processor = Arc::clone(&self.packet_processor);

        // Clone device for both tasks
        let device_read = Arc::clone(device);
        let device_write = Arc::clone(device);

        let tx_clone = tx.clone();
        let is_running_clone = Arc::clone(&is_running);

        // Spawn packet reading task
        tokio::spawn(async move {
            let mut buffer = vec![0u8; 2048];

            while *is_running_clone.read().await {
                match device_read.recv(&mut buffer).await {
                    Ok(size) => {
                        if size > 0 {
                            let packet = buffer[..size].to_vec();
                            if let Err(e) = tx_clone.send(packet).await {
                                error!("Failed to send packet to processor: {}", e);
                                break;
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to read from TUN device: {}", e);
                        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    }
                }
            }
            debug!("Packet reading task stopped");
        });

        // Spawn packet processing task
        tokio::spawn(async move {
            while let Some(packet) = rx.recv().await {
                if let Err(e) = Self::process_packet(
                    packet,
                    &device_write,
                    &packet_processor,
                    &dns_manager,
                    &traffic_router,
                    &backend_proxy,
                ).await {
                    error!("Failed to process packet: {}", e);
                }
            }
            debug!("Packet processing task stopped");
        });

        info!("Packet processing loop started");
        Ok(())
    }

    /// Process a single packet
    async fn process_packet(
        packet: Vec<u8>,
        device: &Arc<AsyncDevice>,
        packet_processor: &PacketProcessor,
        dns_manager: &Arc<dyn DnsManager>,
        traffic_router: &Arc<dyn TrafficRouter>,
        backend_proxy: &Arc<dyn BackendProxy>,
    ) -> Result<()> {
        // Parse the packet
        let parsed = packet_processor.parse_packet(&packet)?;

        match parsed {
            crate::packet::ParsedPacket::Tcp {
                src_addr,
                dst_addr,
                src_port,
                dst_port,
                flags,
                payload
            } => {
                debug!("TCP packet: {}:{} -> {}:{}", src_addr, src_port, dst_addr, dst_port);

                // Handle TCP connection state
                let connection_key = format!("{}:{}->{}:{}", src_addr, src_port, dst_addr, dst_port);

                // Record traffic statistics
                record_traffic(0, packet.len() as u64); // Incoming packet

                // Check if this is a SYN packet (new connection)
                if flags & 0x02 != 0 { // SYN flag
                    packet_processor.track_tcp_connection(&connection_key, TcpState::SynSent).await;
                    record_connection_start(); // New connection

                    // Determine routing for this connection
                    let dest_domain = dns_manager.get_domain_for_fake_ip(dst_addr).await?;
                    let route_target = if let Some(domain) = dest_domain {
                        traffic_router.route_request(&domain, Some(dst_addr), ProxyMode::Rules).await?
                    } else {
                        traffic_router.route_request(&dst_addr.to_string(), Some(dst_addr), ProxyMode::Rules).await?
                    };

                    if let Some(_node_id) = route_target {
                        // Route through proxy
                        let response = backend_proxy.handle_tcp_stream(
                            std::net::SocketAddr::new(src_addr, src_port),
                            std::net::SocketAddr::new(dst_addr, dst_port),
                            payload,
                        ).await?;

                        // Send response back through TUN
                        if !response.is_empty() {
                            let response_packet = packet_processor.build_tcp_response(
                                dst_addr, dst_port, src_addr, src_port, &response
                            )?;
                            record_traffic(response_packet.len() as u64, 0); // Outgoing response
                            if let Err(e) = device.send(&response_packet).await {
                                warn!("Failed to send TCP response: {}", e);
                            }
                        }
                    } else {
                        // Direct connection - let it pass through
                        debug!("Allowing direct connection to {}", dst_addr);
                    }
                }

                // Check if this is a FIN packet (connection closing)
                if flags & 0x01 != 0 { // FIN flag
                    record_connection_end();
                }
            }

            crate::packet::ParsedPacket::Udp {
                src_addr,
                dst_addr,
                src_port,
                dst_port,
                payload
            } => {
                debug!("UDP packet: {}:{} -> {}:{}", src_addr, src_port, dst_addr, dst_port);

                // Record traffic statistics
                record_traffic(0, packet.len() as u64); // Incoming packet

                // Check if this is a DNS query (port 53)
                if dst_port == 53 {
                    let dns_response = dns_manager.handle_dns_query(&payload).await?;
                    let response_packet = packet_processor.build_udp_response(
                        dst_addr, dst_port, src_addr, src_port, &dns_response
                    )?;
                    record_traffic(response_packet.len() as u64, 0); // Outgoing response
                    if let Err(e) = device.send(&response_packet).await {
                        warn!("Failed to send DNS response: {}", e);
                    }
                } else {
                    // Handle regular UDP traffic
                    let dest_domain = dns_manager.get_domain_for_fake_ip(dst_addr).await?;
                    let route_target = if let Some(domain) = dest_domain {
                        traffic_router.route_request(&domain, Some(dst_addr), ProxyMode::Rules).await?
                    } else {
                        traffic_router.route_request(&dst_addr.to_string(), Some(dst_addr), ProxyMode::Rules).await?
                    };

                    if let Some(_node_id) = route_target {
                        // Route through proxy
                        let response = backend_proxy.handle_udp_packet(
                            std::net::SocketAddr::new(src_addr, src_port),
                            std::net::SocketAddr::new(dst_addr, dst_port),
                            payload,
                        ).await?;

                        // Send response back through TUN
                        if !response.is_empty() {
                            let response_packet = packet_processor.build_udp_response(
                                dst_addr, dst_port, src_addr, src_port, &response
                            )?;
                            record_traffic(response_packet.len() as u64, 0); // Outgoing response
                            if let Err(e) = device.send(&response_packet).await {
                                warn!("Failed to send UDP response: {}", e);
                            }
                        }
                    }
                }
            }

            crate::packet::ParsedPacket::Other => {
                debug!("Ignoring non-TCP/UDP packet");
            }
        }

        Ok(())
    }

    /// Get TUN interface IP address
    pub fn get_tun_ip(&self) -> IpAddr {
        IpAddr::V4(self.tun_ip)
    }

    /// Check if TUN interface is running
    pub async fn is_running(&self) -> bool {
        *self.is_running.read().await
    }
}
