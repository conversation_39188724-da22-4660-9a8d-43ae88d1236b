use anyhow::{Result, anyhow};
use std::net::{IpAddr, Ipv4Addr};
use std::collections::HashMap;
use std::time::{SystemTime, Duration};
use tokio::sync::RwLock;
use etherparse::{Ipv<PERSON><PERSON><PERSON><PERSON>, Tcp<PERSON>ead<PERSON>, Udp<PERSON>eader, PacketBuilder};
use log::{debug, warn};

/// TCP connection state
#[derive(Debu<PERSON>, <PERSON>lone, PartialEq)]
pub enum TcpState {
    SynSent,
    SynReceived,
    Established,
    FinWait1,
    FinWait2,
    CloseWait,
    Closing,
    LastAck,
    TimeWait,
    Closed,
}

/// TCP connection information
#[derive(Debug, <PERSON><PERSON>)]
pub struct TcpConnection {
    pub state: TcpState,
    pub local_seq: u32,
    pub remote_seq: u32,
    pub local_ack: u32,
    pub remote_ack: u32,
    pub window_size: u16,
    pub last_activity: SystemTime,
    pub pending_data: Vec<u8>,
}

impl TcpConnection {
    pub fn new(initial_seq: u32, remote_seq: u32) -> Self {
        Self {
            state: TcpState::SynSent,
            local_seq: initial_seq,
            remote_seq,
            local_ack: remote_seq.wrapping_add(1),
            remote_ack: 0,
            window_size: 65535,
            last_activity: SystemTime::now(),
            pending_data: Vec::new(),
        }
    }

    pub fn update_activity(&mut self) {
        self.last_activity = SystemTime::now();
    }

    pub fn is_expired(&self, timeout: Duration) -> bool {
        self.last_activity.elapsed().unwrap_or(Duration::from_secs(0)) > timeout
    }
}

/// Parsed packet information
#[derive(Debug)]
pub enum ParsedPacket {
    Tcp {
        src_addr: IpAddr,
        dst_addr: IpAddr,
        src_port: u16,
        dst_port: u16,
        flags: u8,
        seq_num: u32,
        ack_num: u32,
        payload: Vec<u8>,
    },
    Udp {
        src_addr: IpAddr,
        dst_addr: IpAddr,
        src_port: u16,
        dst_port: u16,
        payload: Vec<u8>,
    },
    Other,
}

/// Packet processor for handling TUN/TAP traffic
pub struct PacketProcessor {
    tcp_connections: RwLock<HashMap<String, TcpConnection>>,
    connection_timeout: Duration,
}

impl PacketProcessor {
    pub fn new() -> Self {
        Self {
            tcp_connections: RwLock::new(HashMap::new()),
            connection_timeout: Duration::from_secs(300), // 5 minutes
        }
    }

    /// Parse a raw packet from TUN interface
    pub fn parse_packet(&self, packet: &[u8]) -> Result<ParsedPacket> {
        if packet.len() < 20 {
            return Ok(ParsedPacket::Other);
        }

        // Parse IP header
        let ip_header = match Ipv4Header::from_slice(packet) {
            Ok((header, _)) => header,
            Err(_) => return Ok(ParsedPacket::Other),
        };

        let src_addr = IpAddr::V4(ip_header.source.into());
        let dst_addr = IpAddr::V4(ip_header.destination.into());
        let ip_header_len = (ip_header.ihl() * 4) as usize;

        if packet.len() < ip_header_len {
            return Ok(ParsedPacket::Other);
        }

        let payload_start = ip_header_len;
        let payload = &packet[payload_start..];

        match ip_header.protocol.0 {
            6 => {
                // TCP
                if payload.len() < 20 {
                    return Ok(ParsedPacket::Other);
                }

                let tcp_header = match TcpHeader::from_slice(payload) {
                    Ok((header, _)) => header,
                    Err(_) => return Ok(ParsedPacket::Other),
                };

                let tcp_header_len = (tcp_header.data_offset() * 4) as usize;
                let tcp_payload = if payload.len() > tcp_header_len {
                    payload[tcp_header_len..].to_vec()
                } else {
                    Vec::new()
                };

                Ok(ParsedPacket::Tcp {
                    src_addr,
                    dst_addr,
                    src_port: tcp_header.source_port,
                    dst_port: tcp_header.destination_port,
                    flags: tcp_header.fin as u8 |
                           (tcp_header.syn as u8) << 1 |
                           (tcp_header.rst as u8) << 2 |
                           (tcp_header.psh as u8) << 3 |
                           (tcp_header.ack as u8) << 4 |
                           (tcp_header.urg as u8) << 5,
                    seq_num: tcp_header.sequence_number,
                    ack_num: tcp_header.acknowledgment_number,
                    payload: tcp_payload,
                })
            }
            17 => {
                // UDP
                if payload.len() < 8 {
                    return Ok(ParsedPacket::Other);
                }

                let udp_header = match UdpHeader::from_slice(payload) {
                    Ok((header, _)) => header,
                    Err(_) => return Ok(ParsedPacket::Other),
                };

                let udp_payload = if payload.len() > 8 {
                    payload[8..].to_vec()
                } else {
                    Vec::new()
                };

                Ok(ParsedPacket::Udp {
                    src_addr,
                    dst_addr,
                    src_port: udp_header.source_port,
                    dst_port: udp_header.destination_port,
                    payload: udp_payload,
                })
            }
            _ => Ok(ParsedPacket::Other),
        }
    }

    /// Track TCP connection with full state management
    pub async fn track_tcp_connection(
        &self,
        connection_key: &str,
        local_seq: u32,
        remote_seq: u32,
        flags: u8,
    ) -> Result<TcpConnection> {
        let mut connections = self.tcp_connections.write().await;

        let connection = if let Some(mut conn) = connections.get(connection_key).cloned() {
            // Update existing connection
            conn.update_activity();

            // Handle TCP state transitions based on flags
            self.update_tcp_state(&mut conn, flags, local_seq, remote_seq)?;

            connections.insert(connection_key.to_string(), conn.clone());
            conn
        } else {
            // Create new connection
            let mut conn = TcpConnection::new(local_seq, remote_seq);
            self.update_tcp_state(&mut conn, flags, local_seq, remote_seq)?;

            connections.insert(connection_key.to_string(), conn.clone());
            debug!("New TCP connection: {} state: {:?}", connection_key, conn.state);
            conn
        };

        Ok(connection)
    }

    /// Update TCP state based on flags and sequence numbers
    fn update_tcp_state(
        &self,
        connection: &mut TcpConnection,
        flags: u8,
        seq: u32,
        ack: u32,
    ) -> Result<()> {
        let syn = flags & 0x02 != 0;
        let ack_flag = flags & 0x10 != 0;
        let fin = flags & 0x01 != 0;
        let rst = flags & 0x04 != 0;

        if rst {
            connection.state = TcpState::Closed;
            return Ok(());
        }

        match connection.state {
            TcpState::SynSent => {
                if syn && ack_flag {
                    connection.state = TcpState::Established;
                    connection.remote_seq = seq;
                    connection.local_ack = seq.wrapping_add(1);
                } else if syn {
                    connection.state = TcpState::SynReceived;
                }
            }
            TcpState::SynReceived => {
                if ack_flag {
                    connection.state = TcpState::Established;
                }
            }
            TcpState::Established => {
                if fin {
                    connection.state = TcpState::CloseWait;
                }
            }
            TcpState::CloseWait => {
                if fin {
                    connection.state = TcpState::LastAck;
                }
            }
            TcpState::FinWait1 => {
                if fin && ack_flag {
                    connection.state = TcpState::TimeWait;
                } else if ack_flag {
                    connection.state = TcpState::FinWait2;
                } else if fin {
                    connection.state = TcpState::Closing;
                }
            }
            TcpState::FinWait2 => {
                if fin {
                    connection.state = TcpState::TimeWait;
                }
            }
            TcpState::Closing => {
                if ack_flag {
                    connection.state = TcpState::TimeWait;
                }
            }
            TcpState::LastAck => {
                if ack_flag {
                    connection.state = TcpState::Closed;
                }
            }
            _ => {}
        }

        // Update sequence numbers
        connection.local_seq = seq;
        connection.remote_ack = ack;

        Ok(())
    }

    /// Get TCP connection state
    pub async fn get_tcp_connection_state(&self, connection_key: &str) -> Option<TcpConnection> {
        let connections = self.tcp_connections.read().await;
        connections.get(connection_key).cloned()
    }

    /// Build TCP response packet
    pub fn build_tcp_response(
        &self,
        src_addr: IpAddr,
        src_port: u16,
        dst_addr: IpAddr,
        dst_port: u16,
        payload: &[u8],
    ) -> Result<Vec<u8>> {
        let src_ipv4 = match src_addr {
            IpAddr::V4(addr) => addr,
            IpAddr::V6(_) => return Err(anyhow!("IPv6 not supported yet")),
        };

        let dst_ipv4 = match dst_addr {
            IpAddr::V4(addr) => addr,
            IpAddr::V6(_) => return Err(anyhow!("IPv6 not supported yet")),
        };

        let builder = PacketBuilder::ipv4(src_ipv4.octets(), dst_ipv4.octets(), 64)
            .tcp(src_port, dst_port, 1000, 1024);

        let mut packet = Vec::new();
        builder.write(&mut packet, payload)
            .map_err(|e| anyhow!("Failed to build TCP packet: {}", e))?;

        Ok(packet)
    }

    /// Build UDP response packet
    pub fn build_udp_response(
        &self,
        src_addr: IpAddr,
        src_port: u16,
        dst_addr: IpAddr,
        dst_port: u16,
        payload: &[u8],
    ) -> Result<Vec<u8>> {
        let src_ipv4 = match src_addr {
            IpAddr::V4(addr) => addr,
            IpAddr::V6(_) => return Err(anyhow!("IPv6 not supported yet")),
        };

        let dst_ipv4 = match dst_addr {
            IpAddr::V4(addr) => addr,
            IpAddr::V6(_) => return Err(anyhow!("IPv6 not supported yet")),
        };

        let builder = PacketBuilder::ipv4(src_ipv4.octets(), dst_ipv4.octets(), 64)
            .udp(src_port, dst_port);

        let mut packet = Vec::new();
        builder.write(&mut packet, payload)
            .map_err(|e| anyhow!("Failed to build UDP packet: {}", e))?;

        Ok(packet)
    }

    /// Clean up old TCP connections
    pub async fn cleanup_old_connections(&self) {
        let mut connections = self.tcp_connections.write().await;
        let mut to_remove = Vec::new();

        for (key, connection) in connections.iter() {
            // Remove closed connections or expired connections
            if matches!(connection.state, TcpState::Closed | TcpState::TimeWait) ||
               connection.is_expired(self.connection_timeout) {
                to_remove.push(key.clone());
            }
        }

        for key in to_remove {
            connections.remove(&key);
            debug!("Cleaned up TCP connection: {}", key);
        }
    }

    /// Extract domain from DNS query
    pub fn extract_domain_from_dns_query(&self, dns_payload: &[u8]) -> Result<Option<String>> {
        if dns_payload.len() < 12 {
            return Ok(None);
        }

        // Skip DNS header (12 bytes)
        let mut pos = 12;
        let mut domain_parts = Vec::new();

        while pos < dns_payload.len() {
            let len = dns_payload[pos] as usize;
            if len == 0 {
                break;
            }

            pos += 1;
            if pos + len > dns_payload.len() {
                return Ok(None);
            }

            let part = String::from_utf8_lossy(&dns_payload[pos..pos + len]);
            domain_parts.push(part.to_string());
            pos += len;
        }

        if domain_parts.is_empty() {
            Ok(None)
        } else {
            Ok(Some(domain_parts.join(".")))
        }
    }

    /// Build DNS response with fake IP
    pub fn build_dns_response(&self, query: &[u8], fake_ip: Ipv4Addr) -> Result<Vec<u8>> {
        if query.len() < 12 {
            return Err(anyhow!("Invalid DNS query"));
        }

        let mut response = query.to_vec();

        // Set response flags (QR=1, AA=1, RA=1)
        response[2] = 0x81;
        response[3] = 0x80;

        // Set answer count to 1
        response[6] = 0x00;
        response[7] = 0x01;

        // Add answer section
        // Name (pointer to question)
        response.extend_from_slice(&[0xc0, 0x0c]);
        
        // Type A (0x0001)
        response.extend_from_slice(&[0x00, 0x01]);
        
        // Class IN (0x0001)
        response.extend_from_slice(&[0x00, 0x01]);
        
        // TTL (300 seconds)
        response.extend_from_slice(&[0x00, 0x00, 0x01, 0x2c]);
        
        // Data length (4 bytes for IPv4)
        response.extend_from_slice(&[0x00, 0x04]);
        
        // IP address
        response.extend_from_slice(&fake_ip.octets());

        Ok(response)
    }
}
