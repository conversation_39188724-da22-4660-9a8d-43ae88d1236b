{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/packet.rs","byte_start":243,"byte_end":247,"line_start":7,"line_end":7,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"use log::{debug, warn};","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/packet.rs","byte_start":241,"byte_end":247,"line_start":7,"line_end":7,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"use log::{debug, warn};","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/packet.rs","byte_start":235,"byte_end":236,"line_start":7,"line_end":7,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use log::{debug, warn};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/packet.rs","byte_start":247,"byte_end":248,"line_start":7,"line_end":7,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"use log::{debug, warn};","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/packet.rs:7:18\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{debug, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/rules.rs","byte_start":179,"byte_end":183,"line_start":7,"line_end":7,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"use log::{debug, info, warn};","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/rules.rs","byte_start":177,"byte_end":183,"line_start":7,"line_end":7,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"use log::{debug, info, warn};","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/rules.rs:7:24\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/proxy.rs","byte_start":180,"byte_end":185,"line_start":7,"line_end":7,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"use log::{info, warn, error, debug};","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/proxy.rs","byte_start":178,"byte_end":185,"line_start":7,"line_end":7,"column_start":21,"column_end":28,"is_primary":true,"text":[{"text":"use log::{info, warn, error, debug};","highlight_start":21,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `error`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/proxy.rs:7:23\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse log::{info, warn, error, debug};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Ipv4Addr`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/route.rs","byte_start":53,"byte_end":61,"line_start":2,"line_end":2,"column_start":24,"column_end":32,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":24,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src/route.rs","byte_start":51,"byte_end":61,"line_start":2,"line_end":2,"column_start":22,"column_end":32,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":22,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/route.rs","byte_start":44,"byte_end":45,"line_start":2,"line_end":2,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/route.rs","byte_start":61,"byte_end":62,"line_start":2,"line_end":2,"column_start":32,"column_end":33,"is_primary":true,"text":[{"text":"use std::net::{IpAddr, Ipv4Addr};","highlight_start":32,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Ipv4Addr`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/route.rs:2:24\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::net::{IpAddr, Ipv4Addr};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"pattern does not mention fields `seq_num`, `ack_num`","code":{"code":"E0027","explanation":"A pattern for a struct fails to specify a sub-pattern for every one of the\nstruct's fields.\n\nErroneous code example:\n\n```compile_fail,E0027\nstruct Dog {\n    name: String,\n    age: u32,\n}\n\nlet d = Dog { name: \"Rusty\".to_string(), age: 8 };\n\n// This is incorrect.\nmatch d {\n    Dog { age: x } => {}\n}\n```\n\nTo fix this error, ensure that each field from the struct's definition is\nmentioned in the pattern, or use `..` to ignore unwanted fields. Example:\n\n```\nstruct Dog {\n    name: String,\n    age: u32,\n}\n\nlet d = Dog { name: \"Rusty\".to_string(), age: 8 };\n\nmatch d {\n    Dog { name: ref n, age: x } => {}\n}\n\n// This is also correct (ignore unused fields).\nmatch d {\n    Dog { age: x, .. } => {}\n}\n```\n"},"level":"error","spans":[{"file_name":"src/tun.rs","byte_start":6963,"byte_end":7162,"line_start":201,"line_end":208,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"            crate::packet::ParsedPacket::Tcp {","highlight_start":13,"highlight_end":47},{"text":"                src_addr,","highlight_start":1,"highlight_end":26},{"text":"                dst_addr,","highlight_start":1,"highlight_end":26},{"text":"                src_port,","highlight_start":1,"highlight_end":26},{"text":"                dst_port,","highlight_start":1,"highlight_end":26},{"text":"                flags,","highlight_start":1,"highlight_end":23},{"text":"                payload","highlight_start":1,"highlight_end":24},{"text":"            } => {","highlight_start":1,"highlight_end":14}],"label":"missing fields `seq_num`, `ack_num`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"include the missing fields in the pattern","code":null,"level":"help","spans":[{"file_name":"src/tun.rs","byte_start":7148,"byte_end":7162,"line_start":207,"line_end":208,"column_start":24,"column_end":14,"is_primary":true,"text":[{"text":"                payload","highlight_start":24,"highlight_end":24},{"text":"            } => {","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":", seq_num, ack_num }","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null},{"message":"if you don't care about these missing fields, you can explicitly ignore them","code":null,"level":"help","spans":[{"file_name":"src/tun.rs","byte_start":7148,"byte_end":7162,"line_start":207,"line_end":208,"column_start":24,"column_end":14,"is_primary":true,"text":[{"text":"                payload","highlight_start":24,"highlight_end":24},{"text":"            } => {","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":", seq_num: _, ack_num: _ }","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null},{"message":"or always ignore missing fields here","code":null,"level":"help","spans":[{"file_name":"src/tun.rs","byte_start":7148,"byte_end":7162,"line_start":207,"line_end":208,"column_start":24,"column_end":14,"is_primary":true,"text":[{"text":"                payload","highlight_start":24,"highlight_end":24},{"text":"            } => {","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":", .. }","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0027]\u001b[0m\u001b[0m\u001b[1m: pattern does not mention fields `seq_num`, `ack_num`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/tun.rs:201:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            crate::packet::ParsedPacket::Tcp {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m202\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                src_addr,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m203\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                dst_addr,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                src_port,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                payload\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m208\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing fields `seq_num`, `ack_num`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: include the missing fields in the pattern\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                payload\u001b[0m\u001b[0m\u001b[38;5;10m, seq_num, ack_num }\u001b[0m\u001b[0m => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you don't care about these missing fields, you can explicitly ignore them\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                payload\u001b[0m\u001b[0m\u001b[38;5;10m, seq_num: _, ack_num: _ }\u001b[0m\u001b[0m => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: or always ignore missing fields here\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m                payload\u001b[0m\u001b[0m\u001b[38;5;10m, .. }\u001b[0m\u001b[0m => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this method takes 4 arguments but 2 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/tun.rs","byte_start":8026,"byte_end":8043,"line_start":225,"line_end":225,"column_start":76,"column_end":93,"is_primary":false,"text":[{"text":"                    packet_processor.track_tcp_connection(&connection_key, TcpState::SynSent).await;","highlight_start":76,"highlight_end":93}],"label":"expected `u32`, found `TcpState`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tun.rs","byte_start":8008,"byte_end":8044,"line_start":225,"line_end":225,"column_start":58,"column_end":94,"is_primary":false,"text":[{"text":"                    packet_processor.track_tcp_connection(&connection_key, TcpState::SynSent).await;","highlight_start":58,"highlight_end":94}],"label":"two arguments of type `u32` and `u8` are missing","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/tun.rs","byte_start":7988,"byte_end":8008,"line_start":225,"line_end":225,"column_start":38,"column_end":58,"is_primary":true,"text":[{"text":"                    packet_processor.track_tcp_connection(&connection_key, TcpState::SynSent).await;","highlight_start":38,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src/packet.rs","byte_start":5450,"byte_end":5464,"line_start":188,"line_end":188,"column_start":9,"column_end":23,"is_primary":false,"text":[{"text":"        local_seq: u32,","highlight_start":9,"highlight_end":23}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/packet.rs","byte_start":5474,"byte_end":5489,"line_start":189,"line_end":189,"column_start":9,"column_end":24,"is_primary":false,"text":[{"text":"        remote_seq: u32,","highlight_start":9,"highlight_end":24}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/packet.rs","byte_start":5499,"byte_end":5508,"line_start":190,"line_end":190,"column_start":9,"column_end":18,"is_primary":false,"text":[{"text":"        flags: u8,","highlight_start":9,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/packet.rs","byte_start":5375,"byte_end":5395,"line_start":185,"line_end":185,"column_start":18,"column_end":38,"is_primary":true,"text":[{"text":"    pub async fn track_tcp_connection(","highlight_start":18,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"provide the arguments","code":null,"level":"help","spans":[{"file_name":"src/tun.rs","byte_start":8008,"byte_end":8044,"line_start":225,"line_end":225,"column_start":58,"column_end":94,"is_primary":true,"text":[{"text":"                    packet_processor.track_tcp_connection(&connection_key, TcpState::SynSent).await;","highlight_start":58,"highlight_end":94}],"label":null,"suggested_replacement":"(&connection_key, /* u32 */, /* u32 */, /* u8 */)","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this method takes 4 arguments but 2 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/tun.rs:225:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    packet_processor.track_tcp_connection(&connection_key, TcpState::SynSent).await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------------------\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mexpected `u32`, found `TcpState`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mtwo arguments of type `u32` and `u8` are missing\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/packet.rs:185:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn track_tcp_connection(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        local_seq: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        remote_seq: u32,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        flags: u8,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: provide the arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                    packet_processor.track_tcp_connection\u001b[0m\u001b[0m\u001b[38;5;9m(&connection_key, TcpState::SynSent)\u001b[0m\u001b[0m.await;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m225\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                    packet_processor.track_tcp_connection\u001b[0m\u001b[0m\u001b[38;5;10m(&connection_key, /* u32 */, /* u32 */, /* u8 */)\u001b[0m\u001b[0m.await;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 2 previous errors; 4 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 2 previous errors; 4 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0027, E0061.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0027, E0061.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0027`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0027`.\u001b[0m\n"}
